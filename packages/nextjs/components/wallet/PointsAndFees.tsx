import React, { useState } from "react";
import { getClaimHandlingFee } from "@/api/user";
import { usePointsAndFees } from "@/hooks/usePointsAndFees";
import { useWalletStore } from "@/services/store/wallet";
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON>Footer, ModalHeader } from "@heroui/react";
import { animated, useSpring } from "@react-spring/web";
import { Award, DollarSign, HelpCircle, RefreshCcw, Shield, TrendingUp } from "lucide-react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface PointsAndFeesProps {
  address: string;
  isActiveWallet: boolean;
}

const PointsAndFeesComponent: React.FC<PointsAndFeesProps> = ({ address, isActiveWallet }) => {
  const { t } = useTranslation();
  const { creds, walletType } = useWalletStore();

  // 使用API Hook获取数据
  const { data: pointsFeesData, loading, refetch } = usePointsAndFees(address, isActiveWallet);

  // 手续费相关状态
  const [isClaimingFees, setIsClaimingFees] = useState<boolean>(false);

  // Modal状态
  const [isPointsModalOpen, setIsPointsModalOpen] = useState<boolean>(false);
  const [isFeesModalOpen, setIsFeesModalOpen] = useState<boolean>(false);

  // 从API数据中提取值
  const currentPoints = pointsFeesData?.credit || 0;
  const pointsMultiplier = pointsFeesData?.credit_ratio || 1.0;
  const totalFees = pointsFeesData?.fee || 0;
  const claimableFees = totalFees; // 假设所有手续费都可领取，可根据业务逻辑调整

  // 动画效果
  const pointsAnimation = useSpring({
    number: currentPoints,
    config: { duration: 500 },
  });

  const feesAnimation = useSpring({
    number: claimableFees,
    config: { duration: 500 },
  });

  // 领取手续费
  const handleClaimFees = async () => {
    if (!address || claimableFees <= 0 || !creds || !walletType) return;

    setIsClaimingFees(true);
    try {
      const response = await getClaimHandlingFee(creds, walletType);

      if (response?.success) {
        toast.success(t("Wallet_Claim_Success") || "手续费领取成功！");
        // 刷新数据以获取最新的手续费信息
        await refetch();
      } else {
        toast.error(t("Wallet_Claim_Failed") || "手续费领取失败，请重试");
      }
    } catch (error) {
      console.error("Claim handling fee error:", error);
      toast.error(t("Wallet_Claim_Error") || "领取过程中发生错误，请重试");
    } finally {
      setIsClaimingFees(false);
    }
  };

  // 刷新所有数据
  const refreshAllData = () => {
    refetch();
  };

  if (!isActiveWallet) {
    return null;
  }

  return (
    <div className="w-full flex flex-col gap-4">
      {/* 积分块 */}
      <div className="w-full flex flex-col p-5 border rounded-lg">
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Points_Title")}</div>
              <HelpCircle
                className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                onClick={() => setIsPointsModalOpen(true)}
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Award className="size-5 text-yellow-500" />
                <div className="text-2xl font-semibold">
                  <animated.div>{pointsAnimation.number.to(n => Math.floor(n).toLocaleString())}</animated.div>
                </div>
              </div>
              <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full">
                <span className="text-sm font-medium text-blue-700">×{pointsMultiplier}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div
              className="flex-center size-10 border rounded-full cursor-pointer hover:bg-gray-100"
              onClick={refreshAllData}
            >
              <RefreshCcw className={`size-4 ${loading ? "animate-spin" : ""}`} />
            </div>
          </div>
        </div>
      </div>

      {/* 手续费块 */}
      <div className="w-full flex flex-col p-5 border rounded-lg">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-3">
              <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Fees_Title")}</div>
              <HelpCircle
                className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                onClick={() => setIsFeesModalOpen(true)}
              />
            </div>

            {/* 美化的数据展示区域 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    <TrendingUp className="size-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 font-medium">{t("Wallet_Total_Fees")}</div>
                    <div className="text-lg font-semibold text-gray-800">${totalFees.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <DollarSign className="size-4 text-green-600" />
                  </div>
                  <div>
                    <div className="text-xs text-green-600 font-medium">{t("Wallet_Claimable_Fees")}</div>
                    <div className="text-xl font-bold text-green-700">
                      $<animated.div className="inline">{feesAnimation.number.to(n => n.toFixed(2))}</animated.div>
                    </div>
                  </div>
                </div>
                {claimableFees > 0 && (
                  <div className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                    {t("Wallet_Ready_To_Claim")}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 领取手续费按钮 */}
        <div className="mt-4">
          <Button
            size="lg"
            variant="solid"
            onPress={handleClaimFees}
            disabled={claimableFees <= 0 || isClaimingFees || !creds || !walletType}
            className={`w-full font-semibold text-sm transition-all duration-200 ${
              claimableFees > 0 && !isClaimingFees && creds && walletType
                ? "bg-green-600 text-white hover:bg-green-700"
                : "bg-gray-200 text-gray-500 cursor-not-allowed"
            }`}
          >
            {isClaimingFees ? (
              <div className="flex items-center gap-2">
                <RefreshCcw className="size-4 animate-spin" />
                {t("Wallet_Claiming_Fees")}
              </div>
            ) : claimableFees > 0 ? (
              <div className="flex items-center gap-2">
                <DollarSign className="size-4" />
                {t("Wallet_Claim_Fees")} $${claimableFees.toFixed(2)}
              </div>
            ) : (
              t("Wallet_No_Fees")
            )}
          </Button>
        </div>
      </div>

      {/* 积分等级体系Modal */}
      <Modal
        isOpen={isPointsModalOpen}
        onClose={() => setIsPointsModalOpen(false)}
        size="3xl"
        scrollBehavior="inside"
        classNames={{
          backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20",
          base: "border-[#292f46] bg-white dark:bg-[#19172c] text-gray-900 dark:text-[#a8b0d3]",
          header: "border-b-[1px] border-gray-200 dark:border-[#292f46]",
          body: "py-6",
          closeButton: "hover:bg-gray-100 dark:hover:bg-white/5 active:bg-gray-200 dark:active:bg-white/10",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
                <Award className="size-5 text-white" />
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {t("Wallet_Points_System_Title")}
              </h3>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                  {t("Wallet_Points_System_Description")}
                </div>
              </div>

              <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                      <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <Award className="size-4 text-yellow-500" />
                          {t("Wallet_Level_Name")}
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="size-4 text-green-500" />
                          {t("Wallet_Monthly_Volume")}
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <DollarSign className="size-4 text-blue-500" />
                          {t("Wallet_Points_Multiplier")}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {[
                      {
                        level: "Wallet_Level_1",
                        volume: "$0 - 1,000",
                        multiplier: "1.0x",
                        color: "from-gray-400 to-gray-500",
                      },
                      {
                        level: "Wallet_Level_2",
                        volume: "$1,001 - 10,000",
                        multiplier: "1.2x",
                        color: "from-green-400 to-green-500",
                      },
                      {
                        level: "Wallet_Level_3",
                        volume: "$10,001 - 100,000",
                        multiplier: "1.5x",
                        color: "from-blue-400 to-blue-500",
                      },
                      {
                        level: "Wallet_Level_4",
                        volume: "$100,001 - 500,000",
                        multiplier: "2.0x",
                        color: "from-purple-400 to-purple-500",
                      },
                      {
                        level: "Wallet_Level_5",
                        volume: "$500,001 - 1,000,000",
                        multiplier: "2.5x",
                        color: "from-pink-400 to-pink-500",
                      },
                      {
                        level: "Wallet_Level_6",
                        volume: "$1,000,001 - 5,000,000",
                        multiplier: "3.5x",
                        color: "from-orange-400 to-orange-500",
                      },
                      {
                        level: "Wallet_Level_7",
                        volume: "> $5,000,000",
                        multiplier: "5.0x",
                        color: "from-yellow-400 to-yellow-500",
                      },
                    ].map((row, index) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${row.color}`}></div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">{t(row.level)}</span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-gray-700 dark:text-gray-300 font-mono">{row.volume}</td>
                        <td className="px-4 py-3">
                          <span
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r ${row.color} text-white`}
                          >
                            {row.multiplier}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <h4 className="font-semibold text-green-800 dark:text-green-400 mb-2 flex items-center gap-2">
                    <Award className="size-4" />
                    {t("Wallet_Points_Calculation")}
                  </h4>
                  <div className="text-sm text-green-700 dark:text-green-300">{t("Wallet_Points_Formula")}</div>
                </div>

                <div className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-400 mb-2 flex items-center gap-2">
                    <RefreshCcw className="size-4" />
                    {t("Wallet_Settlement_Cycle")}
                  </h4>
                  <div className="text-sm text-blue-700 dark:text-blue-300">{t("Wallet_Settlement_Description")}</div>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="primary" variant="light" onPress={() => setIsPointsModalOpen(false)} className="font-medium">
              {t("Normal_Close") || "Close"}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 交易手续费Modal */}
      <Modal
        isOpen={isFeesModalOpen}
        onClose={() => setIsFeesModalOpen(false)}
        size="md"
        classNames={{
          backdrop: "bg-black/50",
          base: "bg-white dark:bg-gray-800",
          header: "border-b border-gray-200 dark:border-gray-700",
          body: "py-4",
          closeButton: "hover:bg-gray-100 dark:hover:bg-gray-700",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex items-center gap-2 pb-3">
            <Shield className="size-5 text-blue-500" />
            <h3 className="text-lg font-semibold">{t("Wallet_Fees_System_Title")}</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                <p className="text-sm text-amber-800 dark:text-amber-300">{t("Wallet_Fees_System_Description")}</p>
              </div>

              <div className="space-y-3">
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <DollarSign className="size-4 text-blue-600" />
                    <h4 className="font-medium text-blue-800 dark:text-blue-400 text-sm">
                      {t("Wallet_Temporary_Collection")}
                    </h4>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    {t("Wallet_Temporary_Collection_Description")}
                  </p>
                </div>

                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <RefreshCcw className="size-4 text-green-600" />
                    <h4 className="font-medium text-green-800 dark:text-green-400 text-sm">
                      {t("Wallet_Full_Refund")}
                    </h4>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300">{t("Wallet_Full_Refund_Description")}</p>
                </div>

                <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Award className="size-4 text-purple-600" />
                    <h4 className="font-medium text-purple-800 dark:text-purple-400 text-sm">
                      {t("Wallet_Free_For_Real_Users")}
                    </h4>
                  </div>
                  <p className="text-xs text-purple-700 dark:text-purple-300">
                    {t("Wallet_Free_For_Real_Users_Description")}
                  </p>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="primary" variant="light" onPress={() => setIsFeesModalOpen(false)} className="font-medium">
              {t("Normal_Close") || "Close"}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default PointsAndFeesComponent;
